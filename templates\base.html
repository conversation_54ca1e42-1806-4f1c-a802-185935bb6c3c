<!DOCTYPE html>
<html dir="rtl" lang="ar">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <title>{% block title %}نظام عتاد{% endblock %}</title>



    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/KSA.png') }}" type="image/png">



    <!-- Updated to use local assets -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-datepicker.min.css') }}">

    <!-- Google Fonts - Arabic Support -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-face.css') }}">

    <!-- Custom RTL overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/rtl-bootstrap.css') }}">



    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <!-- Custom Fixes CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-fixes.css') }}">
    <!-- Dropdown Fixes CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dropdown-fixes.css') }}">
    <!-- Navbar Fixes CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar-fixes.css') }}">
    <!-- Search Box Fixes CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-fixes.css') }}">
    <!-- Theme Upgrades CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-upgrades.css') }}">
    <!-- Display Screens CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/display-screens.css') }}">
    <!-- Device Buttons CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/device-buttons.css') }}">
    <!-- Weapon Details Buttons CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/weapon-details-buttons.css') }}">
    <!-- Barcode Styles CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/barcode-styles.css') }}">
    <!-- Personnel Modal CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/personnel-modal.css') }}">
    <!-- Collapsible Panels CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/collapsible-panels.css') }}">
    <!-- Notifications CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">
    <!-- Details Table CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/details-table.css') }}">
    <!-- Dropdown Position Fix CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dropdown-position-fix.css') }}">
    <!-- Datepicker Fix CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/datepicker-fix.css') }}">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">


    <!-- Additional styles -->
    {% block styles %}{% endblock %}
</head>

<body>
    <!-- Alerts container -->
    <div id="alerts-container" class="position-fixed"
        style="bottom: 45px; left: 25px; width: fit-content; z-index: 9999;">
        {% with messages = get_flashed_messages(with_categories=true, category_filter=['success', 'error', 'danger',
        'warning', 'info']) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert" style="padding-right: 50px;">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}
    </div>

    <!-- Notifications container -->
    <div id="notifications-container"></div>

    {% if current_user.is_authenticated %}
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand text-center">
            <img src="{{ url_for('static', filename='images/KSA.png') }}" alt="شعار المملكة"
                style="width: 80px; height: 80px; margin: 10px auto;">
            <h3 style="margin-top: 0; margin-bottom: 15px; color: #ffffff; font-size: 1.3rem;">نظام عتاد</h3>
        </div>

        <ul class="sidebar-nav">
            {% if not current_user.is_company_duty %}
            <li class="nav-item">
                <a href="{{ url_for('warehouse.dashboard') }}"
                    class="nav-link {% if request.endpoint == 'warehouse.dashboard' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('warehouse.manage') }}"
                    class="nav-link {% if request.endpoint.startswith('warehouse.') and request.endpoint != 'warehouse.dashboard' %}active{% endif %}">
                    <i class="fas fa-warehouse"></i>
                    <span>المستودعات</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('weapons.index') }}"
                    class="nav-link {% if request.endpoint.startswith('weapons.') %}active{% endif %}">
                    <i class="fas fa-crosshairs"></i>
                    <span>الأسلحة</span>
                </a>
            </li>
            {% endif %}

            {% if not current_user.is_company_duty %}
            <li class="nav-item">
                <a href="{{ url_for('statuses.index') }}"
                    class="nav-link {% if request.endpoint.startswith('statuses.') %}active{% endif %}">
                    <i class="fas fa-exchange-alt"></i>
                    <span>الحالات</span>
                </a>
            </li>
            {% endif %}

            {% if current_user.is_company_duty %}
            <li class="nav-item">
                <a href="{{ url_for('statuses.index') }}"
                    class="nav-link {% if request.endpoint.startswith('statuses.') %}active{% endif %}">
                    <i class="fas fa-exchange-alt"></i>
                    <span>الحالات</span>
                </a>
            </li>
            {% endif %}

            {% if not current_user.is_company_duty %}
            <li class="nav-item">
                <a href="{{ url_for('personnel.index') }}"
                    class="nav-link {% if request.endpoint.startswith('personnel.') %}active{% endif %}">
                    <i class="fas fa-users"></i>
                    <span>الأفراد</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('standalone_inventory.index') }}"
                    class="nav-link {% if request.endpoint.startswith('standalone_inventory.') %}active{% endif %}">
                    <i class="fas fa-warehouse"></i>
                    <span>المخزون</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('inventory.devices') }}"
                    class="nav-link {% if request.endpoint == 'inventory.devices' or request.endpoint == 'inventory.device_details' or request.endpoint == 'inventory.create_device' or request.endpoint == 'inventory.edit_device' or request.endpoint == 'inventory.warehouse_devices' %}active{% endif %}">
                    <i class="fas fa-desktop"></i>
                    <span>الأجهزة</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('inventory.audits') }}"
                    class="nav-link {% if request.endpoint == 'inventory.audits' or request.endpoint == 'inventory.audit_details' or request.endpoint == 'inventory.create_audit' %}active{% endif %}">
                    <i class="fas fa-clipboard-check"></i>
                    <span>الجرد</span>
                    {% set alerts_count = get_audit_alerts_count() %}
                    {% if alerts_count > 0 %}
                        <span class="badge badge-warning ml-1">{{ alerts_count }}</span>
                    {% endif %}
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('reports.index') }}"
                    class="nav-link {% if request.endpoint.startswith('reports.') and request.endpoint != 'reports.backup' %}active{% endif %}">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
            </li>
            {% endif %}

            <li class="nav-item">
                <a href="{{ url_for('receipts.index') }}"
                    class="nav-link {% if request.endpoint.startswith('receipts.') %}active{% endif %}">
                    <i class="fas fa-clipboard-list"></i>
                    <span>كشف الاستلامات</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('duties.index') }}"
                    class="nav-link {% if request.endpoint.startswith('duties.') %}active{% endif %}">
                    <i class="fas fa-tasks"></i>
                    <span>كشف الواجبات</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('locations.index') }}"
                    class="nav-link {% if request.endpoint.startswith('locations.') %}active{% endif %}">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>إدارة المواقع</span>
                </a>
            </li>

            {% if current_user.is_admin_role %}
            <li class="nav-item">
                <a href="{{ url_for('auth.users') }}"
                    class="nav-link {% if request.endpoint == 'auth.users' %}active{% endif %}">
                    <i class="fas fa-user-shield"></i>
                    <span>المستخدمين</span>
                </a>
            </li>

            <li class="nav-item">
                <a href="{{ url_for('reports.backup') }}"
                    class="nav-link {% if request.endpoint == 'reports.backup' %}active{% endif %}">
                    <i class="fas fa-database"></i>
                    <span>النسخ الاحتياطي</span>
                </a>
            </li>
            {% endif %}

            <li class="nav-item mt-5">
                <a href="{{ url_for('auth.logout') }}" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <nav class="navbar navbar-expand-lg">
            <button id="toggle-sidebar" class="toggle-sidebar" type="button">
                <i class="fas fa-bars"></i>
            </button>



            <!-- اسم المستخدم على اليمين -->
            <div class="ms-auto navbar-user">
                <span class="navbar-text">
                    <i class="fas fa-user-circle"></i>
                    مرحباً {{ current_user.full_name }}
                </span>
            </div>

            <!-- الأدوات على اليسار بتباعد بينها -->
            <div class="navbar-left-tools d-flex align-items-center gap-3">
                <!-- البحث في النهاية على اليسار -->
                <form class="form-inline" id="search-form"
                    action="{% block search_url %}{{ url_for('weapons.search') }}{% endblock %}" method="GET">
                    <div class="input-group">
                        <div class="input-group-append">
                            <button class="btn" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <input type="text" id="search-input" name="q" class="form-control" placeholder="بحث..."
                            aria-label="Search">
                    </div>
                </form>

                <!-- زر تغيير المظهر -->
                <button id="theme-switcher" class="btn btn-link nav-link">
                    <i class="fas fa-sun"></i>
                </button>

                <!-- زر الإعدادات -->
                <div class="dropdown">
                    <button type="button" id="settingsButton" class="btn btn-link nav-link">
                        <i class="fas fa-cog"></i>
                    </button>

                    <div id="settingsDropdown" class="dropdown-menu dropdown-menu-end">
                        <h6 class="dropdown-header">إعدادات المستخدم</h6>
                        <a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid py-3">
            <h1 class="mb-4">{% block page_title %}{{ title }}{% endblock %}</h1>

            {% block content %}{% endblock %}
        </div>
    </div>
    {% else %}
    {% block auth_content %}{% endblock %}
    {% endif %}

    <!-- Ensure jQuery is loaded before other libraries -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>



    <!-- JavaScript Libraries -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/qrcode.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap-datepicker.ar.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/Chart.js') }}"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/datepicker-init.js') }}"></script>
    <script src="{{ url_for('static', filename='js/device-datepicker-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/charts.js') }}"></script>
    <script src="{{ url_for('static', filename='js/qr-generator.js') }}"></script>
    <script src="{{ url_for('static', filename='js/warehouse-stats.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dropdown-fixes.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dropdown-position-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/weapons-dropdown-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/weapons-dropdown-absolute-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/collapsible-panels.js') }}"></script>
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>


    <script src="{{ url_for('static', filename='js/bootstrap-bundle.js') }}"></script>

    <!-- Error handling for invalid chart data -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            try {
                // تحقق من عدم وجود رسوم بيانية مخصصة في الصفحة قبل تهيئة الرسوم العامة
                const hasCustomCharts = document.querySelector('script[data-custom-charts]') !== null;
                if (!hasCustomCharts) {
                    initializeCharts();
                }
            } catch (error) {
                console.error('Error initializing charts:', error);
            }
        });
    </script>

    <!-- سكريبت مباشر للتحكم في قائمة الإعدادات -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // حل مشكلة قائمة الإعدادات
            const settingsButton = document.getElementById('settingsButton');
            const settingsDropdown = document.getElementById('settingsDropdown');

            if (settingsButton && settingsDropdown) {
                // تبديل القائمة عند النقر على الزر
                settingsButton.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    settingsDropdown.classList.toggle('show');
                });

                // إخفاء القائمة عند النقر خارجها
                document.addEventListener('click', function (e) {
                    if (!settingsButton.contains(e.target) && !settingsDropdown.contains(e.target)) {
                        settingsDropdown.classList.remove('show');
                    }
                });
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>

</html>